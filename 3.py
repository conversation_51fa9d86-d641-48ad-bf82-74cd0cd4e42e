import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from sklearn.preprocessing import MinMaxScaler
from sklearn.model_selection import train_test_split
from sklearn.metrics import r2_score, mean_squared_error, mean_absolute_error, accuracy_score
from keras.models import Sequential
from keras.layers import LSTM, Dense, Dropout
import os
import warnings
import matplotlib

warnings.filterwarnings('ignore')

#设置中文字体和显示负号
matplotlib.rcParams['font.sans-serif'] = ['SimHei']
matplotlib.rcParams['axes.unicode_minus'] = False

print("开始CPU使用率预测模型训练...")

#如果存在clean.csv文件，直接读取，跳过读取原始数据的步骤
if os.path.exists('clean.csv'):
    print("发现已有处理好的数据文件clean.csv，直接读取...")
    try:
        clean_data = pd.read_csv('clean.csv')
        print(f"从clean.csv读取数据，共 {len(clean_data)} 行")

        # 检查数据是否包含所需列
        required_cols = ['cpu_usage', 'hour']
        if all(col in clean_data.columns for col in required_cols):
            # 检查数据是否足够
            if len(clean_data) < 100:
                print("警告: clean.csv中的数据量过少，将重新处理原始数据")
                need_process_original = True
            else:
                need_process_original = False
        else:
            print("clean.csv中缺少必要的列，将重新处理原始数据")
            need_process_original = True
    except Exception as e:
        print(f"读取clean.csv时出错: {e}，将重新处理原始数据")
        need_process_original = True
else:
    print("未找到clean.csv文件，将处理原始数据...")
    need_process_original = True


# 检查数据文件是否存在
if os.path.exists('sorted_data.csv') and need_process_original:
    try:
        # 读取原始数据（只读取前10000行）
        data = pd.read_csv('sorted_data.csv')
        print(f"已读取数据，共 {len(data)} 行")

        # 检查数据中是否包含所需列
        if 'cpu_usage' in data.columns and 'hour' in data.columns:
            # 提取需要的特征列：cpu_usage和hour
            clean_data = data[['cpu_usage', 'hour']].copy()


            # 检查数据类型并转换为数值型
            for col in ['cpu_usage', 'hour']:
                if clean_data[col].dtype == 'object':
                    print(f"将 {col} 列转换为数值类型")
                    clean_data[col] = pd.to_numeric(clean_data[col], errors='coerce')

            print(f"提取后的数据形状:{clean_data.shape}")
            # 处理可能的缺失值
            na_count_before = clean_data.isna().sum().sum()
            clean_data = clean_data.dropna()
            clean_data = clean_data[(clean_data['cpu_usage'] >= 0) & (clean_data['cpu_usage'] <= 100)]
            outliers_removed = na_count_before - len(clean_data)

            print(f"移除了 {outliers_removed} 行超出正常范围(0-100%)的CPU使用率数据")
            print(f"清洗前缺失值数量: {na_count_before}")
            print(f"清洗后的数据，共 {len(clean_data)} 行")

            #只选取数据集的前max_row行
            max_row = 1000
            clean_data = clean_data.head(max_row)
            print(f"只使用前 {max_row} 行数据")

            # 检查是否有足够的数据进行训练
            if len(clean_data) < 100:
                print("警告: 有效数据量过少，将使用合成数据进行演示")
                use_synthetic_data = True
                exit(0)
            else:
                use_synthetic_data = False
        else:
            print("数据集中缺少必要的列 (cpu_usage 或 hour)")
            use_synthetic_data = True
    except Exception as e:
        print(f"处理数据时出错: {e}")
        use_synthetic_data = True
else:
    #print("找不到数据文件 'sorted_data.csv'")
    use_synthetic_data = True

# 如果原始数据不可用或不足，创建合成数据
if 'use_synthetic_data' not in locals() or (use_synthetic_data and need_process_original):
    print("创建合成CPU使用率数据用于演示...")
    np.random.seed(42)

    # 创建一个更真实的CPU使用率时间序列数据
    # 基础负载 + 每日周期变化 + 随机波动
    n_samples = 5000
    hours = np.arange(n_samples) % 24  # 小时循环 (0-23)

    # 基础负载 (20-40%)
    base_load = 30 + np.random.normal(0, 5, n_samples)

    # 每日周期变化 (工作时间负载高)
    daily_pattern = 15 * np.sin(np.pi * hours / 12)

    # 随机波动
    noise = np.random.normal(0, 10, n_samples)

    # 组合所有成分
    cpu_usage = base_load + daily_pattern + noise

    # 确保值在合理范围内 (0-100%)
    cpu_usage = np.clip(cpu_usage, 0, 100)

    # 创建数据框
    clean_data = pd.DataFrame({
        'cpu_usage': cpu_usage,
        'hour': hours
    })

    print(f"已创建合成数据，共 {len(clean_data)} 行")


# 准备用于LSTM模型的数据
# 使用cpu_usage作为目标变量
cpu_usage_data = clean_data['cpu_usage'].values.reshape(-1, 1)

# 归一化数据
scaler = MinMaxScaler(feature_range=(0, 1))
scaled_data = scaler.fit_transform(cpu_usage_data)

# 设置时间窗口大小（减小窗口大小以适应较小的数据集）
window_size = 15
X = []
y = []
indices = np.arange(len(scaled_data))[window_size:]  # 用于跟踪索引

for i in range(window_size, len(scaled_data)):
    X.append(scaled_data[i - window_size:i, 0])
    y.append(scaled_data[i, 0])

X = np.array(X)
y = np.array(y)

# 分割训练集和测试集
X_train, X_test, y_train, y_test, indices_train, indices_test = train_test_split(
    X, y, indices, test_size=0.2, shuffle=False
)

# 重塑数据以适应LSTM输入格式
X_train = X_train.reshape((X_train.shape[0], X_train.shape[1], 1))
X_test = X_test.reshape((X_test.shape[0], X_test.shape[1], 1))

# 构建更简单的LSTM模型（减少单元数量和层数）
model = Sequential()
model.add(LSTM(units=128, return_sequences=True, input_shape=(X_train.shape[1], 1)))
model.add(Dropout(0.2))
model.add(LSTM(units=64))
model.add(Dropout(0.2))
model.add(Dense(1))

model.compile(optimizer='adam', loss='mean_squared_error')

# 训练模型（减少epochs数量）
print("开始训练模型...")
history = model.fit(X_train, y_train, epochs=50, batch_size=32, validation_split=0.1, verbose=1)

# 预测
print("开始进行预测...")
predictions = model.predict(X_test, verbose=0)
predictions = scaler.inverse_transform(predictions).flatten()
y_test_actual = scaler.inverse_transform(y_test.reshape(-1, 1)).flatten()

# 计算评估指标
mse = mean_squared_error(y_test_actual, predictions)
rmse = np.sqrt(mse)
mae = mean_absolute_error(y_test_actual, predictions)
r2 = r2_score(y_test_actual, predictions)
print(f'均方误差 (MSE): {mse:.4f}')
print(f'均方根误差 (RMSE): {rmse:.4f}')
print(f'平均绝对误差 (MAE): {mae:.4f}')
print(f'决定系数 (R²): {r2:.4f}')

# 计算准确率 (ACC)
# 对于回归问题，我们需要定义一个容忍度来计算准确率
# 这里我们定义如果预测值与实际值的相对误差小于20%，则认为预测正确
tolerance = 0.2  # 20%的容忍度
correct_predictions = 0
for i in range(len(y_test_actual)):
    if y_test_actual[i] == 0:  # 避免除以零
        if abs(predictions[i]) < 1.0:  # 如果实际值为0，预测值小于1视为正确
            correct_predictions += 1
    else:
        relative_error = abs(y_test_actual[i] - predictions[i]) / y_test_actual[i]
        if relative_error <= tolerance:
            correct_predictions += 1

accuracy = correct_predictions / len(y_test_actual)
print(f'准确率 (ACC): {accuracy:.4f} (容忍度: {tolerance*100}%)')

# 可视化结果
print("生成可视化结果...")

# 创建一个包含两个子图的图表
fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10))

# 第一个子图：时间序列预测
display_limit = min(100, len(indices_test))
ax1.plot(range(display_limit), y_test_actual[:display_limit], 'b-', linewidth=2, label='实际CPU使用率')
ax1.plot(range(display_limit), predictions[:display_limit], 'r--', linewidth=2, label='预测CPU使用率')
ax1.set_title('CPU使用率预测结果对比', fontsize=14)
ax1.set_xlabel('时间序列索引', fontsize=12)
ax1.set_ylabel('CPU使用率', fontsize=12)
ax1.legend(fontsize=12)
ax1.grid(True)

# 第二个子图：实际值vs预测值散点图
ax2.scatter(y_test_actual, predictions, alpha=0.5)
ax2.plot([min(y_test_actual), max(y_test_actual)], [min(y_test_actual), max(y_test_actual)], 'k--', linewidth=2)
ax2.set_title('实际值 vs 预测值', fontsize=14)
ax2.set_xlabel('实际CPU使用率', fontsize=12)
ax2.set_ylabel('预测CPU使用率', fontsize=12)
ax2.grid(True)
ax2.text(0.05, 0.95, f'R² = {r2:.4f}\nRMSE = {rmse:.4f}\nACC = {accuracy:.4f}',
         transform=ax2.transAxes, fontsize=12, verticalalignment='top',
         bbox=dict(boxstyle='round', facecolor='white', alpha=0.5))

plt.tight_layout()
plt.savefig('cpu_prediction.png', dpi=300)
print("预测结果图像已保存为cpu_prediction.png")

# 绘制训练历史
plt.figure(figsize=(10, 6))
plt.plot(history.history['loss'], label='训练损失')
if 'val_loss' in history.history:
    plt.plot(history.history['val_loss'], label='验证损失')
plt.title('模型训练历史')
plt.xlabel('Epoch')
plt.ylabel('损失值')
plt.legend()
plt.grid(True)
plt.savefig('training_history.png')
print("训练历史图像已保存为training_history.png")

# 打印一些预测样本
print("\n预测样本对比 (实际值 vs 预测值):")
print("-" * 80)
print(f"{'样本序号':^10}{'实际值':^15}{'预测值':^15}{'绝对误差':^15}{'相对误差':^15}{'是否准确':^10}")
print("-" * 80)

for i in range(min(10, len(y_test_actual))):
    actual = y_test_actual[i]
    pred = predictions[i]
    abs_error = abs(actual - pred)

    if actual == 0:
        rel_error = "N/A"
        is_accurate = "是" if abs_error < 1.0 else "否"
    else:
        rel_error = abs_error / actual * 100
        is_accurate = "是" if rel_error <= tolerance * 100 else "否"
        rel_error = f"{rel_error:.2f}%"

    print(f"{i+1:^10}{actual:^15.4f}{pred:^15.4f}{abs_error:^15.4f}{rel_error:^15}{is_accurate:^10}")

print("-" * 80)
print(f"总体评估指标: R² = {r2:.4f}, RMSE = {rmse:.4f}, ACC = {accuracy:.4f}")
print("-" * 80)
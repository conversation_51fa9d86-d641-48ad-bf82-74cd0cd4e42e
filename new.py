import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from sklearn.preprocessing import MinMaxScaler
from sklearn.model_selection import train_test_split
from sklearn.metrics import r2_score, mean_squared_error, mean_absolute_error
from keras.models import Sequential
from keras.layers import LSTM, Dense, Dropout, Conv1D, MaxPooling1D, Flatten
from keras.optimizers import Adam
from keras.callbacks import EarlyStopping, ReduceLROnPlateau
import tensorflow as tf
from statsmodels.tsa.arima.model import ARIMA
import os
import warnings
from datetime import datetime
import matplotlib

warnings.filterwarnings('ignore')

# 检查GPU可用性并配置
print("检查GPU可用性...")
gpus = tf.config.experimental.list_physical_devices('GPU')
if gpus:
    try:
        for gpu in gpus:
            tf.config.experimental.set_memory_growth(gpu, True)
        print(f"发现 {len(gpus)} 个GPU设备，将使用GPU加速训练")
    except RuntimeError as e:
        print(f"GPU配置错误: {e}")
        print("将使用CPU训练")
else:
    print("未发现GPU设备，将使用CPU训练")

# 设置中文字体
matplotlib.rcParams['font.sans-serif'] = ['SimHei']
matplotlib.rcParams['axes.unicode_minus'] = False

# 创建输出目录
current_time = datetime.now().strftime("%Y%m%d_%H%M%S")
output_dir = f"output/{current_time}"
os.makedirs(output_dir, exist_ok=True)
print(f"输出目录: {output_dir}")

def load_and_preprocess_data():
    """加载和预处理数据"""
    print("开始加载和预处理数据...")

    if os.path.exists('sorted_data.csv'):
        try:
            data = pd.read_csv('sorted_data.csv')
            print(f"已读取原始数据，共 {len(data)} 行")

            if 'cpu_usage' in data.columns and 'hour' in data.columns and 'date' in data.columns:
                clean_data = data[['cpu_usage', 'hour', 'date']].copy()

                # 数据类型转换
                for col in ['cpu_usage', 'hour']:
                    if clean_data[col].dtype == 'object':
                        clean_data[col] = pd.to_numeric(clean_data[col], errors='coerce')

                # 处理缺失值
                clean_data = clean_data.dropna()

                # 数据清洗：去除负数和极端值
                before_filter = len(clean_data)
                clean_data = clean_data[clean_data['cpu_usage'] >= 0]

                # 去除极端值
                Q1 = clean_data['cpu_usage'].quantile(0.25)
                Q3 = clean_data['cpu_usage'].quantile(0.75)
                IQR = Q3 - Q1
                lower_bound = max(0, Q1 - 3 * IQR)
                upper_bound = Q3 + 3 * IQR

                clean_data = clean_data[
                    (clean_data['cpu_usage'] >= lower_bound) &
                    (clean_data['cpu_usage'] <= upper_bound)
                ]

                after_filter = len(clean_data)
                print(f"移除了 {before_filter - after_filter} 行异常数据")

                # 构建时间序列
                clean_data['datetime'] = pd.to_datetime(clean_data['date'], format='%Y/%m/%d')
                clean_data['full_datetime'] = clean_data['datetime'] + pd.to_timedelta(clean_data['hour'], unit='h')
                clean_data = clean_data.sort_values('full_datetime').reset_index(drop=True)

                first_time = clean_data['full_datetime'].iloc[0]
                clean_data['time_series'] = (clean_data['full_datetime'] - first_time).dt.total_seconds() / 3600

                # 保留前5000行数据
                max_row = min(5000, len(clean_data))
                clean_data = clean_data.head(max_row)
                print(f"保留前 {max_row} 行数据用于训练")

                # 只保留时间序列和cpu_usage列
                final_data = clean_data[['time_series', 'cpu_usage']].copy()

                # 保存到clean.csv
                final_data.to_csv('clean.csv', index=False)
                print("已保存处理后的数据到clean.csv")

                return final_data
            else:
                print("数据集中缺少必要的列")
                return create_synthetic_data()
        except Exception as e:
            print(f"处理数据时出错: {e}")
            return create_synthetic_data()
    else:
        print("找不到数据文件，将使用合成数据")
        return create_synthetic_data()

def create_synthetic_data():
    """创建合成数据"""
    print("创建合成CPU使用率数据...")
    np.random.seed(42)

    n_samples = 5000
    time_series = np.arange(n_samples)

    # 基础负载 + 周期变化 + 随机波动
    base_load = 40 + np.random.normal(0, 8, n_samples)
    daily_pattern = 20 * np.sin(np.pi * time_series / 12)
    noise = np.random.normal(0, 15, n_samples)

    cpu_usage = base_load + daily_pattern + noise
    cpu_usage = np.maximum(cpu_usage, 0)

    data = pd.DataFrame({
        'time_series': time_series,
        'cpu_usage': cpu_usage
    })

    data.to_csv('clean.csv', index=False)
    print("已保存合成数据到clean.csv")

    return data

def prepare_lstm_data(data, window_size=30):
    """准备LSTM数据"""
    cpu_usage_data = data['cpu_usage'].values.reshape(-1, 1)

    # 使用RobustScaler替代MinMaxScaler，对异常值更鲁棒
    from sklearn.preprocessing import RobustScaler
    scaler = RobustScaler()
    scaled_data = scaler.fit_transform(cpu_usage_data)

    X, y = [], []
    for i in range(window_size, len(scaled_data)):
        X.append(scaled_data[i - window_size:i, 0])
        y.append(scaled_data[i, 0])

    X = np.array(X)
    y = np.array(y)

    print(f"时间窗口大小: {window_size}")
    print(f"生成的序列数量: {len(X)}")

    # 分割数据
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.2, shuffle=False
    )

    # 重塑为LSTM格式
    X_train = X_train.reshape((X_train.shape[0], X_train.shape[1], 1))
    X_test = X_test.reshape((X_test.shape[0], X_test.shape[1], 1))

    return X_train, X_test, y_train, y_test, scaler

def build_lstm_model(input_shape):
    """构建改进的LSTM模型"""
    model = Sequential()

    # 增加模型复杂度和正则化
    model.add(LSTM(units=128, return_sequences=True, input_shape=input_shape))
    model.add(Dropout(0.3))
    model.add(LSTM(units=64, return_sequences=True))
    model.add(Dropout(0.3))
    model.add(LSTM(units=32))
    model.add(Dropout(0.2))
    model.add(Dense(16, activation='relu'))
    model.add(Dense(1))

    # 使用更小的学习率
    optimizer = Adam(learning_rate=0.0005, clipnorm=1.0)
    model.compile(optimizer=optimizer, loss='huber_loss', metrics=['mae'])

    return model

def build_lstm_cnn_model(input_shape):
    """构建改进的LSTM+CNN模型"""
    model = Sequential()

    # 改进CNN部分
    model.add(Conv1D(filters=32, kernel_size=3, activation='relu', input_shape=input_shape, padding='same'))
    model.add(Conv1D(filters=64, kernel_size=3, activation='relu', padding='same'))
    model.add(Dropout(0.2))

    # 不使用MaxPooling，保持序列长度
    model.add(LSTM(units=64, return_sequences=True))
    model.add(Dropout(0.3))
    model.add(LSTM(units=32))
    model.add(Dropout(0.2))
    model.add(Dense(16, activation='relu'))
    model.add(Dense(1))

    # 使用更小的学习率
    optimizer = Adam(learning_rate=0.0005, clipnorm=1.0)
    model.compile(optimizer=optimizer, loss='huber_loss', metrics=['mae'])

    return model

def train_lstm_model(model, X_train, y_train, model_name):
    """训练LSTM模型"""
    print(f"训练{model_name}模型...")

    # 改进回调函数
    early_stopping = EarlyStopping(
        monitor='val_loss',
        patience=20,
        restore_best_weights=True,
        verbose=1
    )
    reduce_lr = ReduceLROnPlateau(
        monitor='val_loss',
        factor=0.5,
        patience=8,
        min_lr=0.00001,
        verbose=1
    )

    # 增加训练轮数和调整批量大小
    history = model.fit(
        X_train, y_train,
        epochs=100,
        batch_size=16,  # 减小批量大小
        validation_split=0.2,
        verbose=1,
        callbacks=[early_stopping, reduce_lr]
    )

    return history

def train_arima_model(data):
    """训练ARIMA模型"""
    print("训练ARIMA模型...")

    cpu_data = data['cpu_usage'].values
    train_size = int(len(cpu_data) * 0.8)
    train_data = cpu_data[:train_size]
    test_data = cpu_data[train_size:]

    try:
        # 使用auto_arima自动选择最佳参数
        from pmdarima import auto_arima

        print("正在自动选择ARIMA参数...")
        auto_model = auto_arima(
            train_data,
            start_p=0, start_q=0,
            max_p=5, max_q=5,
            seasonal=False,
            stepwise=True,
            suppress_warnings=True,
            error_action='ignore',
            max_order=None,
            trace=False
        )

        print(f"选择的ARIMA参数: {auto_model.order}")

        # 预测
        predictions = auto_model.predict(n_periods=len(test_data))

        return predictions, test_data, auto_model

    except ImportError:
        print("pmdarima未安装，使用手动调优的ARIMA模型...")
        try:
            # 尝试多个ARIMA参数组合
            best_aic = float('inf')
            best_model = None
            best_order = None

            for p in range(0, 4):
                for d in range(0, 2):
                    for q in range(0, 4):
                        try:
                            model = ARIMA(train_data, order=(p, d, q))
                            model_fit = model.fit()
                            if model_fit.aic < best_aic:
                                best_aic = model_fit.aic
                                best_model = model_fit
                                best_order = (p, d, q)
                        except:
                            continue

            if best_model is not None:
                print(f"选择的ARIMA参数: {best_order}, AIC: {best_aic:.2f}")
                predictions = best_model.forecast(steps=len(test_data))
                return predictions, test_data, best_model
            else:
                raise Exception("无法找到合适的ARIMA参数")

        except Exception as e:
            print(f"ARIMA模型训练失败: {e}")
            # 使用改进的基线预测：指数平滑
            alpha = 0.3
            predictions = []
            last_value = train_data[-1]

            for _ in range(len(test_data)):
                predictions.append(last_value)
                # 简单的趋势调整
                if len(predictions) > 1:
                    trend = train_data[-10:].mean() - train_data[-20:-10].mean()
                    last_value = last_value + alpha * trend

            return np.array(predictions), test_data, None

    except Exception as e:
        print(f"ARIMA模型训练失败: {e}")
        # 使用移动平均作为基线
        window = min(10, len(train_data) // 4)
        ma_value = np.mean(train_data[-window:])
        predictions = np.full(len(test_data), ma_value)
        return predictions, test_data, None

def calculate_metrics(y_true, y_pred):
    """计算评估指标"""
    mse = mean_squared_error(y_true, y_pred)
    rmse = np.sqrt(mse)
    r2 = r2_score(y_true, y_pred)

    return mse, rmse, r2

def save_results(results, output_dir):
    """保存结果到文件"""
    results_df = pd.DataFrame(results)
    results_df.to_csv(f"{output_dir}/model_comparison.csv", index=False)
    print(f"结果已保存到 {output_dir}/model_comparison.csv")

def plot_results(lstm_pred, lstm_cnn_pred, arima_pred, y_test_actual, output_dir):
    """绘制预测结果对比图"""
    plt.figure(figsize=(15, 10))

    # 子图1: 时间序列预测对比
    plt.subplot(2, 2, 1)
    display_limit = min(100, len(y_test_actual))
    x_axis = range(display_limit)

    plt.plot(x_axis, y_test_actual[:display_limit], 'b-', linewidth=2, label='实际值', alpha=0.8)
    plt.plot(x_axis, lstm_pred[:display_limit], 'r--', linewidth=2, label='LSTM预测', alpha=0.8)
    plt.plot(x_axis, lstm_cnn_pred[:display_limit], 'g--', linewidth=2, label='LSTM+CNN预测', alpha=0.8)
    plt.plot(x_axis, arima_pred[:display_limit], 'm--', linewidth=2, label='ARIMA预测', alpha=0.8)

    plt.title('CPU使用率预测结果对比', fontsize=14)
    plt.xlabel('时间点', fontsize=12)
    plt.ylabel('CPU使用率', fontsize=12)
    plt.legend()
    plt.grid(True, alpha=0.3)

    # 子图2: LSTM散点图
    plt.subplot(2, 2, 2)
    plt.scatter(y_test_actual, lstm_pred, alpha=0.6, color='red')
    plt.plot([y_test_actual.min(), y_test_actual.max()],
             [y_test_actual.min(), y_test_actual.max()], 'k--', linewidth=2)
    plt.title('LSTM: 实际值 vs 预测值', fontsize=14)
    plt.xlabel('实际值', fontsize=12)
    plt.ylabel('预测值', fontsize=12)
    plt.grid(True, alpha=0.3)

    # 子图3: LSTM+CNN散点图
    plt.subplot(2, 2, 3)
    plt.scatter(y_test_actual, lstm_cnn_pred, alpha=0.6, color='green')
    plt.plot([y_test_actual.min(), y_test_actual.max()],
             [y_test_actual.min(), y_test_actual.max()], 'k--', linewidth=2)
    plt.title('LSTM+CNN: 实际值 vs 预测值', fontsize=14)
    plt.xlabel('实际值', fontsize=12)
    plt.ylabel('预测值', fontsize=12)
    plt.grid(True, alpha=0.3)

    # 子图4: ARIMA散点图
    plt.subplot(2, 2, 4)
    plt.scatter(y_test_actual, arima_pred, alpha=0.6, color='magenta')
    plt.plot([y_test_actual.min(), y_test_actual.max()],
             [y_test_actual.min(), y_test_actual.max()], 'k--', linewidth=2)
    plt.title('ARIMA: 实际值 vs 预测值', fontsize=14)
    plt.xlabel('实际值', fontsize=12)
    plt.ylabel('预测值', fontsize=12)
    plt.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig(f"{output_dir}/prediction_comparison.png", dpi=300, bbox_inches='tight')
    plt.close()

    print(f"预测对比图已保存到 {output_dir}/prediction_comparison.png")

def plot_metrics_comparison(results, output_dir):
    """绘制评估指标对比图"""
    models = [result['Model'] for result in results]
    mse_values = [result['MSE'] for result in results]
    rmse_values = [result['RMSE'] for result in results]
    r2_values = [result['R2'] for result in results]

    fig, axes = plt.subplots(1, 3, figsize=(15, 5))

    # MSE对比
    axes[0].bar(models, mse_values, color=['red', 'green', 'magenta'], alpha=0.7)
    axes[0].set_title('MSE对比', fontsize=14)
    axes[0].set_ylabel('MSE', fontsize=12)
    axes[0].tick_params(axis='x', rotation=45)

    # RMSE对比
    axes[1].bar(models, rmse_values, color=['red', 'green', 'magenta'], alpha=0.7)
    axes[1].set_title('RMSE对比', fontsize=14)
    axes[1].set_ylabel('RMSE', fontsize=12)
    axes[1].tick_params(axis='x', rotation=45)

    # R²对比
    axes[2].bar(models, r2_values, color=['red', 'green', 'magenta'], alpha=0.7)
    axes[2].set_title('R²对比', fontsize=14)
    axes[2].set_ylabel('R²', fontsize=12)
    axes[2].tick_params(axis='x', rotation=45)

    plt.tight_layout()
    plt.savefig(f"{output_dir}/metrics_comparison.png", dpi=300, bbox_inches='tight')
    plt.close()

    print(f"指标对比图已保存到 {output_dir}/metrics_comparison.png")

def main():
    """主函数"""
    print("=" * 60)
    print("CPU使用率预测模型对比分析")
    print("=" * 60)

    # 1. 加载和预处理数据
    data = load_and_preprocess_data()
    print(f"数据形状: {data.shape}")
    print(f"CPU使用率范围: {data['cpu_usage'].min():.2f} - {data['cpu_usage'].max():.2f}")

    # 2. 准备LSTM数据
    X_train, X_test, y_train, y_test, scaler = prepare_lstm_data(data)
    print(f"训练集形状: {X_train.shape}, 测试集形状: {X_test.shape}")

    # 3. 训练LSTM模型
    lstm_model = build_lstm_model((X_train.shape[1], X_train.shape[2]))
    lstm_history = train_lstm_model(lstm_model, X_train, y_train, "LSTM")

    # 4. 训练LSTM+CNN模型
    lstm_cnn_model = build_lstm_cnn_model((X_train.shape[1], X_train.shape[2]))
    lstm_cnn_history = train_lstm_model(lstm_cnn_model, X_train, y_train, "LSTM+CNN")

    # 5. 训练ARIMA模型
    arima_pred, arima_test, arima_model = train_arima_model(data)

    # 6. 进行预测
    print("进行预测...")
    lstm_pred_scaled = lstm_model.predict(X_test, verbose=0)
    lstm_cnn_pred_scaled = lstm_cnn_model.predict(X_test, verbose=0)

    # 反归一化LSTM预测结果
    lstm_pred = scaler.inverse_transform(lstm_pred_scaled).flatten()
    lstm_cnn_pred = scaler.inverse_transform(lstm_cnn_pred_scaled).flatten()
    y_test_actual = scaler.inverse_transform(y_test.reshape(-1, 1)).flatten()

    # 确保ARIMA预测结果长度匹配
    min_length = min(len(lstm_pred), len(arima_pred))
    lstm_pred = lstm_pred[:min_length]
    lstm_cnn_pred = lstm_cnn_pred[:min_length]
    y_test_actual = y_test_actual[:min_length]
    arima_pred = arima_pred[:min_length]

    # 7. 计算评估指标
    print("计算评估指标...")
    lstm_mse, lstm_rmse, lstm_r2 = calculate_metrics(y_test_actual, lstm_pred)
    lstm_cnn_mse, lstm_cnn_rmse, lstm_cnn_r2 = calculate_metrics(y_test_actual, lstm_cnn_pred)
    arima_mse, arima_rmse, arima_r2 = calculate_metrics(y_test_actual, arima_pred)

    # 8. 整理结果
    results = [
        {'Model': 'LSTM', 'MSE': lstm_mse, 'RMSE': lstm_rmse, 'R2': lstm_r2},
        {'Model': 'LSTM+CNN', 'MSE': lstm_cnn_mse, 'RMSE': lstm_cnn_rmse, 'R2': lstm_cnn_r2},
        {'Model': 'ARIMA', 'MSE': arima_mse, 'RMSE': arima_rmse, 'R2': arima_r2}
    ]

    # 9. 打印结果
    print("\n" + "=" * 60)
    print("模型性能对比结果")
    print("=" * 60)
    for result in results:
        print(f"{result['Model']:10} | MSE: {result['MSE']:8.4f} | RMSE: {result['RMSE']:8.4f} | R²: {result['R2']:8.4f}")

    # 10. 保存结果和图像
    save_results(results, output_dir)
    plot_results(lstm_pred, lstm_cnn_pred, arima_pred, y_test_actual, output_dir)
    plot_metrics_comparison(results, output_dir)

    print(f"\n所有结果已保存到目录: {output_dir}")
    print("分析完成！")

if __name__ == "__main__":
    main()

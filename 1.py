import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from sklearn.preprocessing import MinMaxScaler, StandardScaler
from sklearn.model_selection import train_test_split
from sklearn.metrics import r2_score, mean_squared_error, mean_absolute_error
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.linear_model import LinearRegression
from keras.models import Sequential
from keras.layers import LSTM, Dense, Dropout
from keras.callbacks import EarlyStopping, ReduceLROnPlateau
import os
import warnings
import matplotlib

warnings.filterwarnings('ignore')

#设置中文字体和显示负号
matplotlib.rcParams['font.sans-serif'] = ['SimHei']
matplotlib.rcParams['axes.unicode_minus'] = False

print("开始CPU使用率预测模型训练...")

# 首先检查clean.csv文件是否存在，如果存在则直接读取
if os.path.exists('clean.csv'):
    print("发现已有处理好的数据文件clean.csv，直接读取...")
    try:
        clean_data = pd.read_csv('clean.csv')
        print(f"从clean.csv读取数据，共 {len(clean_data)} 行")

        # 检查数据是否包含所需列
        required_cols = ['cpu_usage', 'hour']
        if all(col in clean_data.columns for col in required_cols):
            # 检查数据是否足够
            if len(clean_data) < 100:
                print("警告: clean.csv中的数据量过少，将重新处理原始数据")
                need_process_original = True
            else:
                need_process_original = False
        else:
            print("clean.csv中缺少必要的列，将重新处理原始数据")
            need_process_original = True
    except Exception as e:
        print(f"读取clean.csv时出错: {e}，将重新处理原始数据")
        need_process_original = True
else:
    print("未找到clean.csv文件，将处理原始数据...")
    need_process_original = True

# 如果需要处理原始数据
if need_process_original:
    if os.path.exists('sorted_data.csv'):
        try:
            # 读取原始数据
            data = pd.read_csv('sorted_data.csv')
            print(f"已读取原始数据，共 {len(data)} 行")

            # 检查数据中是否包含所需列
            if 'cpu_usage' in data.columns and 'hour' in data.columns:
                # 提取更多特征列，不仅仅是cpu_usage和hour
                # 假设原始数据包含更多有用特征如内存使用率、网络流量等
                useful_features = ['cpu_usage', 'hour']
                # 添加可能存在的其他有用特征
                for col in ['memory_usage', 'network_usage', 'disk_usage']:
                    if col in data.columns:
                        useful_features.append(col)
                
                # 提取有用的特征列
                clean_data = data[useful_features].copy()
                
                # 检查数据类型并转换为数值型
                for col in clean_data.columns:
                    if clean_data[col].dtype == 'object':
                        print(f"将 {col} 列转换为数值类型")
                        clean_data[col] = pd.to_numeric(clean_data[col], errors='coerce')

                # 处理可能的缺失值
                na_count_before = clean_data.isna().sum().sum()
                clean_data = clean_data.dropna()
                print(f"清洗前缺失值数量: {na_count_before}")
                print(f"清洗后的数据，共 {len(clean_data)} 行")
                
                # 处理异常值 - CPU使用率应该在0-100%之间
                print("处理CPU使用率异常值...")
                outliers_before = len(clean_data)
                clean_data = clean_data[(clean_data['cpu_usage'] >= 0) & (clean_data['cpu_usage'] <= 100)]
                outliers_removed = outliers_before - len(clean_data)
                print(f"移除了 {outliers_removed} 行超出正常范围(0-100%)的CPU使用率数据")
                
                # 按hour排序数据
                print("按hour排序数据...")
                clean_data = clean_data.sort_values(by=['hour'])
                
                # 不再随机抽样，而是保持时间序列的连续性
                max_row = 1000
                if len(clean_data) > max_row:
                    print(f"保留最近的 {max_row} 行数据以保持时间序列连续性...")
                    clean_data = clean_data.iloc[-max_row:]
                    print(f"截取后的数据，共 {len(clean_data)} 行")
                
                # 重置索引并添加序列ID列
                clean_data = clean_data.reset_index(drop=True)
                clean_data['seq_id'] = range(1, len(clean_data) + 1)
                
                # 检查是否有足够的数据进行训练
                if len(clean_data) < 100:
                    print("警告: 有效数据量过少，将使用合成数据进行演示")
                    use_synthetic_data = True
                else:
                    use_synthetic_data = False
            else:
                print("数据集中缺少必要的列 (cpu_usage 或 hour)")
                use_synthetic_data = True
        except Exception as e:
            print(f"处理数据时出错: {e}")
            use_synthetic_data = True
    else:
        print("找不到数据文件 'sorted_data.csv'")
        use_synthetic_data = True

    # 如果原始数据不可用或不足，创建合成数据
    if 'use_synthetic_data' not in locals() or use_synthetic_data:
        print("创建合成CPU使用率数据用于演示...")
        np.random.seed(42)

        # 创建一个更真实的CPU使用率时间序列数据
        n_samples = 1000
        hours = np.arange(n_samples) % 24  # 小时循环 (0-23)
        
        # 序列ID
        seq_id = np.arange(1, n_samples + 1)

        # 基础负载 - 使用有趋势的基础负载
        trend = np.linspace(30, 40, n_samples)  # 逐渐增加的趋势
        base_load = trend + np.random.normal(0, 3, n_samples)

        # 每日周期变化 (工作时间负载高)
        day_of_week = np.tile(np.arange(7), (n_samples + 6) // 7)[:n_samples]  # 0-6表示周一到周日
        
        # 工作日负载高于周末
        weekday_effect = np.where(day_of_week < 5, 5, -5)
        
        # 每日周期变化 (上午和下午负载高于夜间)
        daily_pattern = 15 * np.sin(np.pi * (hours + 6) / 12)  # 偏移使得负载高峰在工作时间
        
        # 周期性趋势 - 每周期模式
        weekly_pattern = 3 * np.sin(2 * np.pi * np.arange(n_samples) / 168)  # 168小时为一周期

        # 随机波动 - 减小随机性
        noise = np.random.normal(0, 3, n_samples)

        # 组合所有成分
        cpu_usage = base_load + daily_pattern + weekday_effect + weekly_pattern + noise

        # 确保值在合理范围内 (0-100%)
        cpu_usage = np.clip(cpu_usage, 5, 95)  # 避免极端值

        # 创建数据框
        clean_data = pd.DataFrame({
            'cpu_usage': cpu_usage,
            'hour': hours,
            'day': day_of_week,
            'seq_id': seq_id
        })
        
        # 增加更多特征：创建滞后特征
        for lag in range(1, 4):
            clean_data[f'cpu_lag_{lag}'] = clean_data['cpu_usage'].shift(lag)
        
        # 创建滚动平均特征
        clean_data['cpu_rolling_mean_3'] = clean_data['cpu_usage'].rolling(window=3).mean()
        clean_data['cpu_rolling_mean_6'] = clean_data['cpu_usage'].rolling(window=6).mean()
        
        # 添加差分特征
        clean_data['cpu_diff_1'] = clean_data['cpu_usage'].diff()
        
        # 创建小时的周期性特征
        clean_data['hour_sin'] = np.sin(2 * np.pi * clean_data['hour'] / 24)
        clean_data['hour_cos'] = np.cos(2 * np.pi * clean_data['hour'] / 24)
        
        # 创建星期的周期性特征
        clean_data['day_sin'] = np.sin(2 * np.pi * clean_data['day'] / 7)
        clean_data['day_cos'] = np.cos(2 * np.pi * clean_data['day'] / 7)
        
        # 处理NaN值
        clean_data = clean_data.dropna()

        print(f"已创建合成数据，共 {len(clean_data)} 行")

    # 保存清洗后的数据到clean.csv
    clean_data.to_csv('clean.csv', index=False)
    print("已保存处理后的数据到clean.csv")

# 准备用于LSTM模型的数据
print("准备数据用于LSTM模型训练...")

# 检查数据分布
print("CPU使用率数据统计:")
print(clean_data['cpu_usage'].describe())

# 检查是否有异常值
q1 = clean_data['cpu_usage'].quantile(0.25)
q3 = clean_data['cpu_usage'].quantile(0.75)
iqr = q3 - q1
lower_bound = q1 - 1.5 * iqr
upper_bound = q3 + 1.5 * iqr
print(f"异常值界限: 下界={lower_bound:.2f}, 上界={upper_bound:.2f}")

# 移除异常值
outliers = clean_data[(clean_data['cpu_usage'] < lower_bound) | (clean_data['cpu_usage'] > upper_bound)]
print(f"检测到 {len(outliers)} 个异常值")

if len(outliers) > 0:
    print("移除异常值...")
    clean_data = clean_data[(clean_data['cpu_usage'] >= lower_bound) & (clean_data['cpu_usage'] <= upper_bound)]
    print(f"移除异常值后的数据量: {len(clean_data)}")

# 对数据进行排序，确保时间序列的连续性
clean_data = clean_data.sort_values(by='hour').reset_index(drop=True)

# 创建更多特征
print("创建额外特征...")
# 添加滞后特征
for lag in range(1, 4):
    clean_data[f'cpu_lag_{lag}'] = clean_data['cpu_usage'].shift(lag)

# 添加滚动平均特征
clean_data['cpu_rolling_mean_3'] = clean_data['cpu_usage'].rolling(window=3).mean()
clean_data['cpu_rolling_mean_6'] = clean_data['cpu_usage'].rolling(window=6).mean()

# 添加小时的周期性特征
clean_data['hour_sin'] = np.sin(2 * np.pi * clean_data['hour'] / 24)
clean_data['hour_cos'] = np.cos(2 * np.pi * clean_data['hour'] / 24)

# 删除NaN值
clean_data = clean_data.dropna()
print(f"添加特征后的数据量: {len(clean_data)}")

# 准备特征和目标变量
features = ['cpu_usage', 'hour', 'cpu_lag_1', 'cpu_lag_2', 'cpu_lag_3',
            'cpu_rolling_mean_3', 'cpu_rolling_mean_6', 'hour_sin', 'hour_cos']
X_data = clean_data[features].values
y_data = clean_data['cpu_usage'].values

# 使用MinMaxScaler进行归一化
scaler_X = MinMaxScaler(feature_range=(0, 1))
scaler_y = MinMaxScaler(feature_range=(0, 1))

# 归一化特征
X_scaled = scaler_X.fit_transform(X_data)
y_scaled = scaler_y.fit_transform(y_data.reshape(-1, 1)).flatten()

# 设置时间窗口
window_size = 15
X = []
y = []
indices = np.arange(len(X_scaled))[window_size:]  # 用于跟踪索引

# 创建序列数据
for i in range(window_size, len(X_scaled)):
    X.append(X_scaled[i-window_size:i])
    y.append(y_scaled[i])

X = np.array(X)
y = np.array(y)

print(f"准备好的数据形状: X={X.shape}, y={y.shape}")

# 分割训练集和测试集
X_train, X_test, y_train, y_test, indices_train, indices_test = train_test_split(
    X, y, indices, test_size=0.2, shuffle=False
)

# 构建更适合的LSTM模型
print("构建LSTM模型...")
model = Sequential()

# 输入形状为 (window_size, num_features)
input_shape = (X_train.shape[1], X_train.shape[2])
print(f"模型输入形状: {input_shape}")

# 使用更简单的模型架构，避免过拟合
model.add(LSTM(units=128, return_sequences=True, input_shape=input_shape))
model.add(Dropout(0.2))
model.add(LSTM(units=64))
model.add(Dropout(0.2))
model.add(Dense(32, activation='relu'))
model.add(Dense(1))

# 使用Adam优化器，设置较小的学习率
from keras.optimizers import Adam
optimizer = Adam(learning_rate=0.001)
model.compile(optimizer=optimizer, loss='mean_squared_error')
model.summary()

# 设置早停和学习率调整回调函数
early_stopping = EarlyStopping(
    monitor='val_loss',
    patience=15,
    verbose=1,
    restore_best_weights=True
)

reduce_lr = ReduceLROnPlateau(
    monitor='val_loss',
    factor=0.8,
    patience=5,
    min_lr=0.0001,
    verbose=1
)

# 训练模型
print("开始训练模型...")
history = model.fit(
    X_train, y_train,
    epochs=100,
    batch_size=32,
    validation_split=0.2,
    verbose=1,
    callbacks=[early_stopping, reduce_lr]
)

# 预测
print("开始进行预测...")
predictions = model.predict(X_test, verbose=0)

# 将预测值和实际值转换回原始尺度
y_test_reshaped = y_test.reshape(-1, 1)
predictions_inverse = scaler_y.inverse_transform(predictions)
y_test_actual = scaler_y.inverse_transform(y_test_reshaped)

# 确保数据是一维数组
predictions = predictions_inverse.flatten()
y_test_actual = y_test_actual.flatten()

# 计算评估指标
mse = mean_squared_error(y_test_actual, predictions)
rmse = np.sqrt(mse)
mae = mean_absolute_error(y_test_actual, predictions)
r2 = r2_score(y_test_actual, predictions)

print(f'均方误差 (MSE): {mse:.4f}')
print(f'均方根误差 (RMSE): {rmse:.4f}')
print(f'平均绝对误差 (MAE): {mae:.4f}')
print(f'决定系数 (R²): {r2:.4f}')

# 可视化结果
print("生成可视化结果...")

# 创建一个包含两个子图的图表
fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10))

# 第一个子图：时间序列预测
display_limit = min(100, len(indices_test))
ax1.plot(range(display_limit), y_test_actual[:display_limit], 'b-', linewidth=2, label='实际CPU使用率')
ax1.plot(range(display_limit), predictions[:display_limit], 'r--', linewidth=2, label='预测CPU使用率')
ax1.set_title('CPU使用率预测结果对比', fontsize=14)
ax1.set_xlabel('样本索引', fontsize=12)
ax1.set_ylabel('CPU使用率', fontsize=12)
ax1.legend(fontsize=12)
ax1.grid(True)

# 第二个子图：实际值vs预测值散点图
ax2.scatter(y_test_actual, predictions, alpha=0.5)
ax2.plot([y_test_actual.min(), y_test_actual.max()], [y_test_actual.min(), y_test_actual.max()], 'k--', linewidth=2)
ax2.set_title('实际值 vs 预测值', fontsize=14)
ax2.set_xlabel('实际CPU使用率', fontsize=12)
ax2.set_ylabel('预测CPU使用率', fontsize=12)
ax2.grid(True)
ax2.text(0.05, 0.95, f'R² = {r2:.4f}\nRMSE = {rmse:.4f}', transform=ax2.transAxes,
         fontsize=12, verticalalignment='top', bbox=dict(boxstyle='round', facecolor='white', alpha=0.5))

plt.tight_layout()
plt.savefig('cpu_prediction.png', dpi=300)
print("预测结果图像已保存为cpu_prediction.png")

# 打印一些预测样本
print("\n预测样本对比 (实际值 vs 预测值):")
for i in range(min(5, len(y_test_actual))):
    error = abs(y_test_actual[i] - predictions[i])
    error_percent = (error / y_test_actual[i]) * 100 if y_test_actual[i] != 0 else float('inf')
    print(f"样本 {i+1}: 实际值 = {y_test_actual[i]:.4f}, 预测值 = {predictions[i]:.4f}, 误差 = {error:.4f} ({error_percent:.2f}%)")

# 绘制训练历史
plt.figure(figsize=(10, 6))
plt.plot(history.history['loss'], label='训练损失')
plt.plot(history.history['val_loss'], label='验证损失')
plt.title('模型训练历史')
plt.xlabel('Epoch')
plt.ylabel('损失值')
plt.legend()
plt.grid(True)
plt.savefig('training_history.png')
print("训练历史图像已保存为training_history.png")
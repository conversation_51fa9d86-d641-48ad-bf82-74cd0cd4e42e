import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from sklearn.preprocessing import MinMaxScaler
from sklearn.model_selection import train_test_split
from sklearn.metrics import r2_score, mean_squared_error, mean_absolute_error, accuracy_score
from keras.models import Sequential
from keras.layers import LSTM, Dense, Dropout
import os
import warnings
import matplotlib
import tensorflow as tf
from datetime import datetime

warnings.filterwarnings('ignore')

# 检查GPU可用性并配置
print("检查GPU可用性...")
gpus = tf.config.experimental.list_physical_devices('GPU')
if gpus:
    try:
        # 设置GPU内存增长
        for gpu in gpus:
            tf.config.experimental.set_memory_growth(gpu, True)
        print(f"发现 {len(gpus)} 个GPU设备，将使用GPU加速训练")
        print(f"GPU设备: {[gpu.name for gpu in gpus]}")
    except RuntimeError as e:
        print(f"GPU配置错误: {e}")
        print("将使用CPU训练")
else:
    print("未发现GPU设备，将使用CPU训练")

#设置中文字体和显示负号
matplotlib.rcParams['font.sans-serif'] = ['SimHei']
matplotlib.rcParams['axes.unicode_minus'] = False

print("开始CPU使用率预测模型训练...")

# 直接读取sorted_data.csv文件，不再使用clean.csv
if os.path.exists('sorted_data.csv'):
    try:
        # 读取原始数据
        print("读取sorted_data.csv文件...")
        data = pd.read_csv('sorted_data.csv')
        print(f"已读取原始数据，共 {len(data)} 行")

        # 检查数据中是否包含所需列
        if 'cpu_usage' in data.columns and 'hour' in data.columns and 'date' in data.columns:
            # 提取需要的特征列：cpu_usage、hour和date
            clean_data = data[['cpu_usage', 'hour', 'date']].copy()

            # 检查数据类型并转换为数值型
            for col in ['cpu_usage', 'hour']:
                if clean_data[col].dtype == 'object':
                    print(f"将 {col} 列转换为数值类型")
                    clean_data[col] = pd.to_numeric(clean_data[col], errors='coerce')

            print(f"提取后的数据形状: {clean_data.shape}")

            # 处理可能的缺失值
            na_count_before = clean_data.isna().sum().sum()
            clean_data = clean_data.dropna()
            print(f"清洗前缺失值数量: {na_count_before}")

            # 数据清洗：去除负数和极端值，但不进行平滑处理
            before_filter = len(clean_data)

            # 去除负数
            clean_data = clean_data[clean_data['cpu_usage'] >= 0]

            # 去除极端值（使用四分位数方法）
            Q1 = clean_data['cpu_usage'].quantile(0.25)
            Q3 = clean_data['cpu_usage'].quantile(0.75)
            IQR = Q3 - Q1
            lower_bound = Q1 - 3 * IQR  # 使用3倍IQR，比1.5倍更宽松
            upper_bound = Q3 + 3 * IQR

            # 确保下界不小于0
            lower_bound = max(0, lower_bound)

            clean_data = clean_data[
                (clean_data['cpu_usage'] >= lower_bound) &
                (clean_data['cpu_usage'] <= upper_bound)
            ]

            after_filter = len(clean_data)
            outliers_removed = before_filter - after_filter

            print(f"移除了 {outliers_removed} 行异常数据（负值和极端值）")
            print(f"数据范围: {clean_data['cpu_usage'].min():.2f} - {clean_data['cpu_usage'].max():.2f}")
            print(f"清洗后的数据，共 {len(clean_data)} 行")

            # 构建时间序列
            print("构建时间序列...")

            # 将date列转换为datetime格式
            clean_data['datetime'] = pd.to_datetime(clean_data['date'], format='%Y/%m/%d')

            # 添加小时信息到datetime
            clean_data['full_datetime'] = clean_data['datetime'] + pd.to_timedelta(clean_data['hour'], unit='h')

            # 按时间排序
            clean_data = clean_data.sort_values('full_datetime').reset_index(drop=True)

            # 计算时间序列（以小时为单位，从第一行开始为0）
            first_time = clean_data['full_datetime'].iloc[0]
            clean_data['time_series'] = (clean_data['full_datetime'] - first_time).dt.total_seconds() / 3600

            print(f"时间序列范围: {clean_data['time_series'].min():.2f} - {clean_data['time_series'].max():.2f} 小时")

            # 保留前5000行数据用于训练
            max_row = min(5000, len(clean_data))
            clean_data = clean_data.head(max_row)
            print(f"保留前 {max_row} 行数据用于训练")

            # 只保留时间序列和cpu_usage列
            clean_data_final = clean_data[['time_series', 'cpu_usage']].copy()

            # 保存到clean.csv
            clean_data_final.to_csv('clean.csv', index=False)
            print("已保存处理后的数据到clean.csv")

            # 检查是否有足够的数据进行训练
            if len(clean_data_final) < 100:
                print("警告: 有效数据量过少，将使用合成数据进行演示")
                use_synthetic_data = True
            else:
                use_synthetic_data = False
                clean_data = clean_data_final
        else:
            print("数据集中缺少必要的列 (cpu_usage、hour 或 date)")
            use_synthetic_data = True
    except Exception as e:
        print(f"处理数据时出错: {e}")
        use_synthetic_data = True
else:
    print("找不到数据文件 'sorted_data.csv'，将使用合成数据")
    use_synthetic_data = True

# 如果原始数据不可用或不足，创建合成数据
if 'use_synthetic_data' not in locals() or use_synthetic_data:
    print("创建合成CPU使用率数据用于演示...")
    np.random.seed(42)

    # 创建一个更真实的CPU使用率时间序列数据
    # 基础负载 + 每日周期变化 + 随机波动
    n_samples = 5000  # 调整为5000行
    time_series = np.arange(n_samples)  # 时间序列从0开始

    # 基础负载 (20-60%)
    base_load = 40 + np.random.normal(0, 8, n_samples)

    # 每日周期变化 (工作时间负载高)
    daily_pattern = 20 * np.sin(np.pi * time_series / 12)

    # 随机波动
    noise = np.random.normal(0, 15, n_samples)

    # 组合所有成分
    cpu_usage = base_load + daily_pattern + noise

    # 确保值在合理范围内
    cpu_usage = np.maximum(cpu_usage, 0)  # 只确保不为负数

    # 创建数据框
    clean_data = pd.DataFrame({
        'time_series': time_series,
        'cpu_usage': cpu_usage
    })

    # 保存到clean.csv
    clean_data.to_csv('clean.csv', index=False)
    print("已保存合成数据到clean.csv")

    print(f"已创建合成数据，共 {len(clean_data)} 行")


# 准备用于LSTM模型的数据
# 使用cpu_usage作为目标变量
cpu_usage_data = clean_data['cpu_usage'].values.reshape(-1, 1)

# 使用MinMaxScaler进行归一化
scaler = MinMaxScaler(feature_range=(0, 1))
scaled_data = scaler.fit_transform(cpu_usage_data)

# 打印原始数据范围和统计信息
print(f"原始CPU使用率范围: 最小值 = {cpu_usage_data.min():.4f}, 最大值 = {cpu_usage_data.max():.4f}")
print(f"原始CPU使用率统计: 均值 = {cpu_usage_data.mean():.4f}, 标准差 = {cpu_usage_data.std():.4f}")
print(f"归一化后的数据范围: 最小值 = {scaled_data.min():.4f}, 最大值 = {scaled_data.max():.4f}")
print(f"归一化后的数据统计: 均值 = {scaled_data.mean():.4f}, 标准差 = {scaled_data.std():.4f}")

# 设置时间窗口大小（减小窗口大小以适应较小的数据集）
window_size = 15
X = []
y = []
indices = np.arange(len(scaled_data))[window_size:]  # 用于跟踪索引

for i in range(window_size, len(scaled_data)):
    X.append(scaled_data[i - window_size:i, 0])
    y.append(scaled_data[i, 0])

X = np.array(X)
y = np.array(y)

print(f"准备好的数据形状: X={X.shape}, y={y.shape}")

# 分割训练集和测试集
X_train, X_test, y_train, y_test, indices_train, indices_test = train_test_split(
    X, y, indices, test_size=0.2, shuffle=False
)

# 重塑数据以适应LSTM输入格式
X_train = X_train.reshape((X_train.shape[0], X_train.shape[1], 1))
X_test = X_test.reshape((X_test.shape[0], X_test.shape[1], 1))

print(f"训练集形状: X_train={X_train.shape}, y_train={y_train.shape}")
print(f"测试集形状: X_test={X_test.shape}, y_test={y_test.shape}")

# 构建改进的LSTM模型
from keras.optimizers import Adam
from keras.callbacks import EarlyStopping, ReduceLROnPlateau
from keras.layers import BatchNormalization

model = Sequential()

# 由于使用归一化数据，需要调整模型架构以适应更大的数值范围
# 添加批量归一化层来稳定训练
model.add(LSTM(units=64, return_sequences=True, input_shape=(X_train.shape[1], X_train.shape[2])))
model.add(BatchNormalization())
model.add(Dropout(0.2))

model.add(LSTM(units=32, return_sequences=True))
model.add(BatchNormalization())
model.add(Dropout(0.2))

model.add(LSTM(units=16))
model.add(BatchNormalization())
model.add(Dropout(0.1))

# 添加全连接层
model.add(Dense(8, activation='relu'))
model.add(Dense(1))

# 使用更小的学习率，因为原始数据的数值范围更大
optimizer = Adam(learning_rate=0.0001, clipnorm=1.0)

# 使用MAE作为损失函数，对异常值更鲁棒
model.compile(optimizer=optimizer, loss='mean_absolute_error', metrics=['mse'])

print("模型架构:")
model.summary()

# 设置回调函数
early_stopping = EarlyStopping(
    monitor='val_loss',
    patience=20,
    verbose=1,
    restore_best_weights=True
)

reduce_lr = ReduceLROnPlateau(
    monitor='val_loss',
    factor=0.2,
    patience=5,
    min_lr=0.00001,
    verbose=1
)

# 训练模型
print("开始训练模型...")
history = model.fit(
    X_train, y_train,
    epochs=100,  # 增加训练轮数
    batch_size=16,  # 减小批量大小
    validation_split=0.2,  # 增加验证集比例
    verbose=1,
    callbacks=[early_stopping, reduce_lr]
)

# 预测
print("开始进行预测...")
predictions = model.predict(X_test, verbose=0)

# 将预测值和实际值转换回原始尺度
y_test_reshaped = y_test.reshape(-1, 1)
predictions_inverse = scaler.inverse_transform(predictions)
y_test_actual = scaler.inverse_transform(y_test_reshaped)

# 确保数据是一维数组
predictions = predictions_inverse.flatten()
y_test_actual = y_test_actual.flatten()

# 打印预测值范围
print(f"预测值范围: 最小值 = {predictions.min():.4f}, 最大值 = {predictions.max():.4f}")
print(f"实际值范围: 最小值 = {y_test_actual.min():.4f}, 最大值 = {y_test_actual.max():.4f}")

# 计算预测误差的分布
prediction_errors = np.abs(predictions - y_test_actual)
print(f"预测误差统计:")
print(f"  平均误差: {prediction_errors.mean():.4f}")
print(f"  误差标准差: {prediction_errors.std():.4f}")
print(f"  最大误差: {prediction_errors.max():.4f}")
print(f"  误差中位数: {np.median(prediction_errors):.4f}")

# 计算评估指标
mse = mean_squared_error(y_test_actual, predictions)
rmse = np.sqrt(mse)
mae = mean_absolute_error(y_test_actual, predictions)
r2 = r2_score(y_test_actual, predictions)
print(f'均方误差 (MSE): {mse:.4f}')
print(f'均方根误差 (RMSE): {rmse:.4f}')
print(f'平均绝对误差 (MAE): {mae:.4f}')
print(f'决定系数 (R²): {r2:.4f}')

# 计算准确率 (ACC)
# 对于回归问题，我们需要定义一个容忍度来计算准确率
# 这里我们定义如果预测值与实际值的相对误差小于20%，则认为预测正确
tolerance = 0.2  # 20%的容忍度
correct_predictions = 0
for i in range(len(y_test_actual)):
    if y_test_actual[i] == 0:  # 避免除以零
        if abs(predictions[i]) < 1.0:  # 如果实际值为0，预测值小于1视为正确
            correct_predictions += 1
    else:
        relative_error = abs(y_test_actual[i] - predictions[i]) / y_test_actual[i]
        if relative_error <= tolerance:
            correct_predictions += 1

accuracy = correct_predictions / len(y_test_actual)
print(f'准确率 (ACC): {accuracy:.4f} (容忍度: {tolerance*100}%)')

# 可视化结果
print("生成可视化结果...")

# 创建一个包含两个子图的图表
fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10))

# 第一个子图：时间序列预测
display_limit = min(100, len(indices_test))
ax1.plot(range(display_limit), y_test_actual[:display_limit], 'b-', linewidth=2, label='实际CPU使用率')
ax1.plot(range(display_limit), predictions[:display_limit], 'r--', linewidth=2, label='预测CPU使用率')
ax1.set_title('CPU使用率预测结果对比', fontsize=14)
ax1.set_xlabel('时间序列索引', fontsize=12)
ax1.set_ylabel('CPU使用率', fontsize=12)
ax1.legend(fontsize=12)
ax1.grid(True)

# 第二个子图：实际值vs预测值散点图
ax2.scatter(y_test_actual, predictions, alpha=0.5)
ax2.plot([min(y_test_actual), max(y_test_actual)], [min(y_test_actual), max(y_test_actual)], 'k--', linewidth=2)
ax2.set_title('实际值 vs 预测值', fontsize=14)
ax2.set_xlabel('实际CPU使用率', fontsize=12)
ax2.set_ylabel('预测CPU使用率', fontsize=12)
ax2.grid(True)
ax2.text(0.05, 0.95, f'R² = {r2:.4f}\nRMSE = {rmse:.4f}\nACC = {accuracy:.4f}',
         transform=ax2.transAxes, fontsize=12, verticalalignment='top',
         bbox=dict(boxstyle='round', facecolor='white', alpha=0.5))

plt.tight_layout()
plt.savefig('cpu_prediction.png', dpi=300)
print("预测结果图像已保存为cpu_prediction.png")

# 绘制训练历史
plt.figure(figsize=(10, 6))
plt.plot(history.history['loss'], label='训练损失')
if 'val_loss' in history.history:
    plt.plot(history.history['val_loss'], label='验证损失')
plt.title('模型训练历史')
plt.xlabel('Epoch')
plt.ylabel('损失值')
plt.legend()
plt.grid(True)
plt.savefig('training_history.png')
print("训练历史图像已保存为training_history.png")

# 打印一些预测样本
print("\n预测样本对比 (实际值 vs 预测值):")
print("-" * 80)
print(f"{'样本序号':^10}{'实际值':^15}{'预测值':^15}{'绝对误差':^15}{'相对误差':^15}{'是否准确':^10}")
print("-" * 80)

for i in range(min(10, len(y_test_actual))):
    actual = y_test_actual[i]
    pred = predictions[i]
    abs_error = abs(actual - pred)

    if actual == 0:
        rel_error = "N/A"
        is_accurate = "是" if abs_error < 1.0 else "否"
    else:
        rel_error = abs_error / actual * 100
        is_accurate = "是" if rel_error <= tolerance * 100 else "否"
        rel_error = f"{rel_error:.2f}%"

    print(f"{i+1:^10}{actual:^15.4f}{pred:^15.4f}{abs_error:^15.4f}{rel_error:^15}{is_accurate:^10}")

print("-" * 80)
print(f"总体评估指标: R² = {r2:.4f}, RMSE = {rmse:.4f}, ACC = {accuracy:.4f}")
print("-" * 80)